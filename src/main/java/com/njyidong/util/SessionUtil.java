package com.njyidong.util;

import com.njyidong.entity.User;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 会话管理工具类
 */
public class SessionUtil {
    
    // 会话中用户信息的键名
    public static final String USER_SESSION_KEY = "user";
    public static final String USER_ID_SESSION_KEY = "userId";
    public static final String USERNAME_SESSION_KEY = "username";
    public static final String USER_TYPE_SESSION_KEY = "userType";
    
    /**
     * 将用户信息存储到会话中
     */
    public static void setUser(HttpServletRequest request, User user) {
        HttpSession session = request.getSession();
        session.setAttribute(USER_SESSION_KEY, user);
        session.setAttribute(USER_ID_SESSION_KEY, user.getId());
        session.setAttribute(USERNAME_SESSION_KEY, user.getUsername());
        session.setAttribute(USER_TYPE_SESSION_KEY, user.getUserType());
    }
    
    /**
     * 从会话中获取用户信息
     */
    public static User getUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute(USER_SESSION_KEY);
        }
        return null;
    }
    
    /**
     * 从会话中获取用户ID
     */
    public static Integer getUserId(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (Integer) session.getAttribute(USER_ID_SESSION_KEY);
        }
        return null;
    }
    
    /**
     * 从会话中获取用户名
     */
    public static String getUsername(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute(USERNAME_SESSION_KEY);
        }
        return null;
    }
    
    /**
     * 从会话中获取用户类型
     */
    public static String getUserType(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute(USER_TYPE_SESSION_KEY);
        }
        return null;
    }
    
    /**
     * 检查用户是否已登录
     */
    public static boolean isLoggedIn(HttpServletRequest request) {
        return getUser(request) != null;
    }
    
    /**
     * 检查用户是否为管理员
     */
    public static boolean isAdmin(HttpServletRequest request) {
        String userType = getUserType(request);
        return "admin".equals(userType);
    }
    
    /**
     * 检查用户是否为商户
     */
    public static boolean isMerchant(HttpServletRequest request) {
        String userType = getUserType(request);
        return "merchant".equals(userType);
    }
    
    /**
     * 清除会话信息（登出）
     */
    public static void logout(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
    }
    
    /**
     * 检查登录状态，未登录则重定向到登录页面
     */
    public static boolean checkLogin(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        if (!isLoggedIn(request)) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return false;
        }
        return true;
    }
    
    /**
     * 检查管理员权限，非管理员则重定向到首页
     */
    public static boolean checkAdmin(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        if (!isAdmin(request)) {
            response.sendRedirect(request.getContextPath() + "/index.jsp");
            return false;
        }
        return true;
    }
    
    /**
     * 设置会话超时时间（分钟）
     */
    public static void setSessionTimeout(HttpServletRequest request, int minutes) {
        HttpSession session = request.getSession();
        session.setMaxInactiveInterval(minutes * 60);
    }
    
    /**
     * 获取会话剩余时间（秒）
     */
    public static int getSessionRemainingTime(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            long lastAccessedTime = session.getLastAccessedTime();
            int maxInactiveInterval = session.getMaxInactiveInterval();
            long currentTime = System.currentTimeMillis();
            long elapsedTime = (currentTime - lastAccessedTime) / 1000;
            return (int) (maxInactiveInterval - elapsedTime);
        }
        return 0;
    }
}
