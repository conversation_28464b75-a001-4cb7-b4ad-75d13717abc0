package com.njyidong.entity;

import java.sql.Timestamp;
import java.util.List;

/**
 * 组群实体类
 */
public class Group {
    private Integer id;
    private String groupName;
    private Integer creatorId;
    private String description;
    private String groupType; // public, private
    private Integer maxMembers;
    private String status; // active, inactive
    private Timestamp createdAt;
    private Timestamp updatedAt;
    
    // 关联信息
    private User creator;
    private List<User> members;
    private Integer memberCount;
    
    // 构造方法
    public Group() {}
    
    public Group(String groupName, Integer creatorId, String description) {
        this.groupName = groupName;
        this.creatorId = creatorId;
        this.description = description;
    }
    
    // Getter和Setter方法
    public Integer getId() {
        return id;
    }
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    public Integer getCreatorId() {
        return creatorId;
    }
    
    public void setCreatorId(Integer creatorId) {
        this.creatorId = creatorId;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getGroupType() {
        return groupType;
    }
    
    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }
    
    public Integer getMaxMembers() {
        return maxMembers;
    }
    
    public void setMaxMembers(Integer maxMembers) {
        this.maxMembers = maxMembers;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Timestamp getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }
    
    public Timestamp getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public User getCreator() {
        return creator;
    }
    
    public void setCreator(User creator) {
        this.creator = creator;
    }
    
    public List<User> getMembers() {
        return members;
    }
    
    public void setMembers(List<User> members) {
        this.members = members;
    }
    
    public Integer getMemberCount() {
        return memberCount;
    }
    
    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }
    
    @Override
    public String toString() {
        return "Group{" +
                "id=" + id +
                ", groupName='" + groupName + '\'' +
                ", creatorId=" + creatorId +
                ", description='" + description + '\'' +
                ", groupType='" + groupType + '\'' +
                ", maxMembers=" + maxMembers +
                ", status='" + status + '\'' +
                ", memberCount=" + memberCount +
                ", createdAt=" + createdAt +
                '}';
    }
}
